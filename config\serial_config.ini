# TEC温控上位机串口配置文件
# 作者: mkx
# 创建日期: 2025-08-05
# 描述: 串口通信相关配置参数

[SERIAL]
# 串口号 (根据实际情况修改)
port = COM1

# 波特率 (固定115200)
baudrate = 115200

# 数据位
bytesize = 8

# 停止位
stopbits = 1

# 校验位 (无校验)
parity = N

# 超时时间 (秒)
timeout = 1.0

# 写超时时间 (秒)
write_timeout = 1.0

[PROTOCOL]
# 帧头字节1
header_byte1 = 0x5A

# 帧头字节2  
header_byte2 = 0xA5

# 上位机发送数据包长度
send_packet_length = 15

# CPU返回数据包长度
receive_packet_length = 7

[LOGGING]
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
log_level = INFO

# 日志文件路径
log_file = logs/tec_controller.log

# 日志格式
log_format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
