#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TEC温控上位机串口通信模块

文件名: serial_comm.py
作者: mkx
创建日期: 2025-08-05
修改日期: 2025-08-05
版本: v1.0.0

功能描述:
    实现RS232串口通信功能，包括串口初始化、数据发送接收、连接管理等基础功能。
    支持波特率115200的TEC温控设备通信。

主要类:
    SerialComm: 串口通信主类

注意事项:
    - 波特率固定为115200
    - 数据位8位，停止位1位，无校验
    - 需要安装pyserial库
"""

import serial
import serial.tools.list_ports
import threading
import time
import logging
from typing import Optional, List, Callable, Any


class SerialComm:
    """
    串口通信类
    
    作者: mkx
    创建日期: 2025-08-05
    
    功能: 提供RS232串口通信的基础功能
    
    属性:
        _serial_port: 串口对象
        _is_connected: 连接状态标志
        _receive_thread: 接收数据线程
        _receive_callback: 数据接收回调函数
        _lock: 线程锁
    
    注意事项:
        - 使用前需要调用connect()方法建立连接
        - 使用完毕后需要调用disconnect()方法断开连接
    """
    
    def __init__(self):
        """
        初始化串口通信对象
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 初始化串口通信相关参数和状态
        """
        self._serial_port: Optional[serial.Serial] = None
        self._is_connected: bool = False
        self._receive_thread: Optional[threading.Thread] = None
        self._receive_callback: Optional[Callable[[bytes], None]] = None
        self._lock = threading.Lock()
        self._stop_receive = False
        
        # 配置日志
        self._logger = logging.getLogger(__name__)
    
    def get_available_ports(self) -> List[str]:
        """
        获取可用串口列表
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 扫描系统中可用的串口设备
        
        返回值:
            List[str]: 可用串口名称列表
        
        注意事项:
            - 返回的串口名称格式为 "COM1", "COM2" 等
        """
        ports = []
        try:
            available_ports = serial.tools.list_ports.comports()
            for port in available_ports:
                ports.append(port.device)
            self._logger.info(f"发现可用串口: {ports}")
        except Exception as e:
            self._logger.error(f"获取串口列表失败: {e}")
        
        return ports
    
    def connect(self, port: str, baudrate: int = 115200, 
                bytesize: int = 8, stopbits: int = 1, 
                parity: str = 'N', timeout: float = 1.0) -> bool:
        """
        连接串口设备
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 建立与指定串口设备的连接
        
        参数:
            port (str): 串口名称，如 "COM1"
            baudrate (int): 波特率，默认115200
            bytesize (int): 数据位，默认8
            stopbits (int): 停止位，默认1
            parity (str): 校验位，默认'N'(无校验)
            timeout (float): 超时时间，默认1.0秒
        
        返回值:
            bool: 连接成功返回True，失败返回False
        
        注意事项:
            - TEC温控设备固定使用115200波特率
            - 连接前会自动断开已有连接
        """
        try:
            # 如果已连接，先断开
            if self._is_connected:
                self.disconnect()
            
            # 创建串口对象
            self._serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=bytesize,
                stopbits=stopbits,
                parity=parity,
                timeout=timeout,
                write_timeout=timeout
            )
            
            # 检查连接状态
            if self._serial_port.is_open:
                self._is_connected = True
                self._stop_receive = False
                
                # 启动接收线程
                self._start_receive_thread()
                
                self._logger.info(f"串口连接成功: {port}, 波特率: {baudrate}")
                return True
            else:
                self._logger.error(f"串口连接失败: {port}")
                return False
                
        except Exception as e:
            self._logger.error(f"串口连接异常: {e}")
            return False
    
    def disconnect(self) -> bool:
        """
        断开串口连接
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 断开当前串口连接并清理资源
        
        返回值:
            bool: 断开成功返回True，失败返回False
        
        注意事项:
            - 会停止接收线程并关闭串口
        """
        try:
            if self._is_connected and self._serial_port:
                # 停止接收线程
                self._stop_receive = True
                if self._receive_thread and self._receive_thread.is_alive():
                    self._receive_thread.join(timeout=2.0)
                
                # 关闭串口
                self._serial_port.close()
                self._serial_port = None
                self._is_connected = False
                
                self._logger.info("串口连接已断开")
                return True
            else:
                self._logger.warning("串口未连接，无需断开")
                return True
                
        except Exception as e:
            self._logger.error(f"断开串口连接异常: {e}")
            return False
    
    def send_data(self, data: bytes) -> bool:
        """
        发送数据到串口
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 向串口设备发送字节数据
        
        参数:
            data (bytes): 要发送的字节数据
        
        返回值:
            bool: 发送成功返回True，失败返回False
        
        注意事项:
            - 发送前会检查连接状态
            - 使用线程锁保证线程安全
        """
        if not self._is_connected or not self._serial_port:
            self._logger.error("串口未连接，无法发送数据")
            return False
        
        try:
            with self._lock:
                bytes_written = self._serial_port.write(data)
                self._serial_port.flush()  # 确保数据发送完毕
                
                self._logger.debug(f"发送数据: {data.hex().upper()}, 字节数: {bytes_written}")
                return bytes_written == len(data)
                
        except Exception as e:
            self._logger.error(f"发送数据异常: {e}")
            return False
    
    def set_receive_callback(self, callback: Callable[[bytes], None]) -> None:
        """
        设置数据接收回调函数
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 设置接收到数据时的回调处理函数
        
        参数:
            callback (Callable[[bytes], None]): 回调函数，接收bytes类型参数
        
        注意事项:
            - 回调函数在接收线程中执行，需要注意线程安全
        """
        self._receive_callback = callback
        self._logger.debug("数据接收回调函数已设置")
    
    def _start_receive_thread(self) -> None:
        """
        启动数据接收线程
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 创建并启动后台数据接收线程
        
        注意事项:
            - 这是内部方法，不应直接调用
        """
        self._receive_thread = threading.Thread(
            target=self._receiveDataLoop,
            daemon=True,
            name="SerialReceiveThread"
        )
        self._receive_thread.start()
        self._logger.debug("数据接收线程已启动")
    
    def _receiveDataLoop(self) -> None:
        """
        数据接收循环
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 持续接收串口数据并调用回调函数
        
        注意事项:
            - 这是内部方法，在接收线程中运行
            - 异常会被捕获并记录日志
        """
        while not self._stop_receive and self._is_connected:
            try:
                if self._serial_port and self._serial_port.in_waiting > 0:
                    data = self._serial_port.read(self._serial_port.in_waiting)
                    if data and self._receive_callback:
                        self._receive_callback(data)
                        self._logger.debug(f"接收数据: {data.hex().upper()}")
                
                time.sleep(0.01)  # 避免CPU占用过高
                
            except Exception as e:
                self._logger.error(f"接收数据异常: {e}")
                break
        
        self._logger.debug("数据接收线程已停止")
    
    @property
    def is_connected(self) -> bool:
        """
        获取连接状态
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 返回当前串口连接状态
        
        返回值:
            bool: 已连接返回True，未连接返回False
        """
        return self._is_connected
    
    def __del__(self):
        """
        析构函数
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 对象销毁时自动断开串口连接
        """
        if self._is_connected:
            self.disconnect()
