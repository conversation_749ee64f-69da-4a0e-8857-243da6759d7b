#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TEC温控指令控制模块

文件名: tec_controller.py
作者: mkx
创建日期: 2025-08-05
修改日期: 2025-08-05
版本: v1.0.0

功能描述:
    实现TEC温控系统的所有指令处理功能，包括温度设置、PID参数调节、
    传感器配置、保护设置等。提供高级接口封装底层通信协议。

主要类:
    TECController: TEC温控器主控制类
    TECStatus: TEC状态数据类

注意事项:
    - 需要配合serial_comm和protocol模块使用
    - 所有温度单位为摄氏度
    - 电流单位为安培，电压单位为伏特
"""

import time
import logging
import threading
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime

from .serial_comm import SerialComm
from .protocol import ProtocolParser, CommandCode, SensorType


@dataclass
class TECStatus:
    """
    TEC状态数据类
    
    作者: mkx
    创建日期: 2025-08-05
    
    功能: 存储TEC温控系统的实时状态数据
    
    属性:
        current_temp: 当前温度
        target_temp: 目标温度
        pid_p: PID P值
        pid_i: PID I值
        pid_d: PID D值
        output_voltage: 输出电压
        output_current: 输出电流
        system_status: 系统状态
        auto_tune_status: 自整定状态
        enable_status: 使能状态
        calibration_status: 校准状态
        display_mode: 显示模式
        last_update: 最后更新时间
    """
    current_temp: float = 0.0
    target_temp: float = 0.0
    pid_p: float = 0.0
    pid_i: float = 0.0
    pid_d: float = 0.0
    output_voltage: float = 0.0
    output_current: float = 0.0
    system_status: Dict[str, Any] = field(default_factory=dict)
    auto_tune_status: Dict[str, Any] = field(default_factory=dict)
    enable_status: Dict[str, Any] = field(default_factory=dict)
    calibration_status: Dict[str, Any] = field(default_factory=dict)
    display_mode: Dict[str, Any] = field(default_factory=dict)
    last_update: datetime = field(default_factory=datetime.now)


class TECController:
    """
    TEC温控器主控制类
    
    作者: mkx
    创建日期: 2025-08-05
    
    功能: 提供TEC温控系统的高级控制接口
    
    属性:
        _serial_comm: 串口通信对象
        _protocol: 协议解析器对象
        _status: TEC状态数据
        _status_callbacks: 状态更新回调函数列表
        _lock: 线程锁
    
    注意事项:
        - 使用前需要先连接串口
        - 支持多个状态更新回调函数
        - 线程安全设计
    """
    
    def __init__(self):
        """
        初始化TEC控制器
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 初始化TEC控制器相关组件和状态
        """
        self._serial_comm = SerialComm()
        self._protocol = ProtocolParser()
        self._status = TECStatus()
        self._status_callbacks = []
        self._lock = threading.Lock()
        
        # 配置日志
        self._logger = logging.getLogger(__name__)
        
        # 设置串口数据接收回调
        self._serial_comm.set_receive_callback(self._on_data_received)
    
    def connect(self, port: str, baudrate: int = 115200) -> bool:
        """
        连接TEC设备
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 建立与TEC温控设备的串口连接
        
        参数:
            port (str): 串口名称
            baudrate (int): 波特率，默认115200
        
        返回值:
            bool: 连接成功返回True，失败返回False
        
        注意事项:
            - TEC设备固定使用115200波特率
        """
        try:
            success = self._serial_comm.connect(port, baudrate)
            if success:
                self._logger.info(f"TEC设备连接成功: {port}")
                # 连接成功后可以开始查询状态
                self._start_status_query()
            else:
                self._logger.error(f"TEC设备连接失败: {port}")
            return success
        except Exception as e:
            self._logger.error(f"连接TEC设备异常: {e}")
            return False
    
    def disconnect(self) -> bool:
        """
        断开TEC设备连接
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 断开与TEC温控设备的串口连接
        
        返回值:
            bool: 断开成功返回True，失败返回False
        """
        try:
            success = self._serial_comm.disconnect()
            if success:
                self._logger.info("TEC设备连接已断开")
            return success
        except Exception as e:
            self._logger.error(f"断开TEC设备连接异常: {e}")
            return False
    
    def get_available_ports(self) -> list:
        """
        获取可用串口列表
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 获取系统中可用的串口设备列表
        
        返回值:
            list: 可用串口名称列表
        """
        return self._serial_comm.get_available_ports()
    
    def set_target_temperature(self, temperature: float) -> bool:
        """
        设置目标温度
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 设置TEC温控的目标温度
        
        参数:
            temperature (float): 目标温度值(摄氏度)
        
        返回值:
            bool: 设置成功返回True，失败返回False
        
        注意事项:
            - 修改温度会重新自整定
        """
        try:
            command = self._protocol.create_set_target_temp_command(temperature)
            success = self._serial_comm.send_data(command)
            if success:
                self._status.target_temp = temperature
                self._logger.info(f"设置目标温度: {temperature}°C")
                self._notify_status_update()
            else:
                self._logger.error(f"设置目标温度失败: {temperature}°C")
            return success
        except Exception as e:
            self._logger.error(f"设置目标温度异常: {e}")
            return False
    
    def set_pid_parameters(self, p_value: float, i_value: float, d_value: float) -> bool:
        """
        设置PID参数
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 设置用户自定义PID控制参数
        
        参数:
            p_value (float): 比例参数P值
            i_value (float): 积分参数I值
            d_value (float): 微分参数D值
        
        返回值:
            bool: 设置成功返回True，失败返回False
        """
        try:
            command = self._protocol.create_set_pid_params_command(p_value, i_value, d_value)
            success = self._serial_comm.send_data(command)
            if success:
                self._logger.info(f"设置PID参数: P={p_value}, I={i_value}, D={d_value}")
            else:
                self._logger.error(f"设置PID参数失败: P={p_value}, I={i_value}, D={d_value}")
            return success
        except Exception as e:
            self._logger.error(f"设置PID参数异常: {e}")
            return False
    
    def set_auto_tune(self, enable: bool) -> bool:
        """
        设置自整定

        作者: mkx
        创建日期: 2025-08-05

        功能: 设置PID自整定功能的使能状态

        参数:
            enable (bool): True为使用自整定，False为使用当前系统内部PID参数

        返回值:
            bool: 设置成功返回True，失败返回False
        """
        try:
            command = self._protocol.create_set_auto_tune_command(enable)
            success = self._serial_comm.send_data(command)
            if success:
                status_text = "开启" if enable else "关闭"
                self._logger.info(f"设置自整定: {status_text}")
            else:
                self._logger.error(f"设置自整定失败: {enable}")
            return success
        except Exception as e:
            self._logger.error(f"设置自整定异常: {e}")
            return False

    def set_max_current(self, max_current: float) -> bool:
        """
        设置最大电流

        作者: mkx
        创建日期: 2025-08-05

        功能: 设置输出最大电流(过流保护)

        参数:
            max_current (float): 最大电流值(安培)

        返回值:
            bool: 设置成功返回True，失败返回False
        """
        try:
            command = self._protocol.create_set_max_current_command(max_current)
            success = self._serial_comm.send_data(command)
            if success:
                self._logger.info(f"设置最大电流: {max_current}A")
            else:
                self._logger.error(f"设置最大电流失败: {max_current}A")
            return success
        except Exception as e:
            self._logger.error(f"设置最大电流异常: {e}")
            return False

    def set_max_voltage(self, max_voltage: float) -> bool:
        """
        设置最大电压

        作者: mkx
        创建日期: 2025-08-05

        功能: 设置输出最大电压(过压保护)

        参数:
            max_voltage (float): 最大电压值(伏特)

        返回值:
            bool: 设置成功返回True，失败返回False
        """
        try:
            command = self._protocol.create_set_max_voltage_command(max_voltage)
            success = self._serial_comm.send_data(command)
            if success:
                self._logger.info(f"设置最大电压: {max_voltage}V")
            else:
                self._logger.error(f"设置最大电压失败: {max_voltage}V")
            return success
        except Exception as e:
            self._logger.error(f"设置最大电压异常: {e}")
            return False

    def set_sensor_type(self, sensor_type: SensorType) -> bool:
        """
        设置传感器类型

        作者: mkx
        创建日期: 2025-08-05

        功能: 设置温度传感器类型

        参数:
            sensor_type (SensorType): 传感器类型枚举值

        返回值:
            bool: 设置成功返回True，失败返回False

        注意事项:
            - 系统初始默认为10K热敏电阻
        """
        try:
            command = self._protocol.create_set_sensor_type_command(sensor_type)
            success = self._serial_comm.send_data(command)
            if success:
                sensor_names = {
                    SensorType.NTC_10K: "10K热敏电阻",
                    SensorType.PT100: "PT100",
                    SensorType.PT1000: "PT1000",
                    SensorType.AD590: "AD590",
                    SensorType.LM35: "LM35"
                }
                sensor_name = sensor_names.get(sensor_type, f"未知传感器({sensor_type})")
                self._logger.info(f"设置传感器类型: {sensor_name}")
            else:
                self._logger.error(f"设置传感器类型失败: {sensor_type}")
            return success
        except Exception as e:
            self._logger.error(f"设置传感器类型异常: {e}")
            return False

    def set_temperature_protection(self, max_temp: float, min_temp: float) -> bool:
        """
        设置过温保护

        作者: mkx
        创建日期: 2025-08-05

        功能: 设置过温保护的最高和最低温度

        参数:
            max_temp (float): 过温保护最高温度(摄氏度)
            min_temp (float): 过温保护最低温度(摄氏度)

        返回值:
            bool: 设置成功返回True，失败返回False
        """
        try:
            command = self._protocol.create_set_temp_protect_command(max_temp, min_temp)
            success = self._serial_comm.send_data(command)
            if success:
                self._logger.info(f"设置过温保护: 最高{max_temp}°C, 最低{min_temp}°C")
            else:
                self._logger.error(f"设置过温保护失败: 最高{max_temp}°C, 最低{min_temp}°C")
            return success
        except Exception as e:
            self._logger.error(f"设置过温保护异常: {e}")
            return False

    def set_ntc_parameters(self, b_value: float, resistance_25c: float) -> bool:
        """
        设置NTC参数

        作者: mkx
        创建日期: 2025-08-05

        功能: 设置NTC热敏电阻的B值和常温阻值

        参数:
            b_value (float): NTC热敏电阻B值
            resistance_25c (float): NTC热敏电阻常温阻值(不支持修改)

        返回值:
            bool: 设置成功返回True，失败返回False

        注意事项:
            - 常温阻值参数不支持修改
        """
        try:
            command = self._protocol.create_set_ntc_params_command(b_value, resistance_25c)
            success = self._serial_comm.send_data(command)
            if success:
                self._logger.info(f"设置NTC参数: B值={b_value}, 常温阻值={resistance_25c}Ω")
            else:
                self._logger.error(f"设置NTC参数失败: B值={b_value}, 常温阻值={resistance_25c}Ω")
            return success
        except Exception as e:
            self._logger.error(f"设置NTC参数异常: {e}")
            return False
