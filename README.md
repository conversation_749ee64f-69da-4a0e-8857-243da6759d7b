# TEC温控上位机软件

## 项目简介
本项目是一个基于Python开发的TEC温控上位机软件，用于与嵌入式设备进行RS232串口通信，实现温度控制、PID参数调节、传感器配置等功能。

## 作者信息
- 作者: mkx
- 创建日期: 2025-08-05
- 版本: v1.0.0

## 功能特性
- RS232串口通信 (波特率115200)
- 严格按照通信协议进行数据交互
- 温度设置和监控
- PID参数调节 (P、I、D值设置)
- 多种传感器支持 (10K热敏电阻、PT100、PT1000、AD590、LM35)
- 过温保护设置
- 自整定功能
- 校准功能
- 实时状态监控

## 通信协议
详细的通信协议请参考 `doc/通信协议.md` 文档。

### 上位机发送格式
| byte1 | byte2 | byte3 | byte4~byte15 |
|-------|-------|-------|--------------|
| 0x5A  | 0xA5  | 控制指令 | 指令数据 |

### CPU返回格式  
| byte1 | byte2 | byte3 | byte4~byte7 |
|-------|-------|-------|-------------|
| 0x5A  | 0xA5  | 控制指令 | 指令数据 |

## 项目结构
```
├── src/                    # 源代码目录
│   ├── main.py            # 主程序入口
│   ├── serial_comm.py     # 串口通信模块
│   ├── protocol.py        # 协议解析模块
│   ├── tec_controller.py  # TEC温控指令模块
│   └── gui.py             # 图形用户界面
├── config/                # 配置文件目录
├── tests/                 # 测试文件目录
├── logs/                  # 日志文件目录
├── doc/                   # 文档目录
└── requirements.txt       # 依赖包配置
```

## 安装和运行
1. 安装Python依赖包：
   ```bash
   pip install -r requirements.txt
   ```

2. 运行程序：
   ```bash
   python src/main.py
   ```

## 使用说明
1. 连接TEC温控设备到计算机的串口
2. 启动上位机软件
3. 选择正确的串口号
4. 设置通信参数 (波特率115200)
5. 开始温控操作

## 注意事项
- 确保串口连接正确
- 波特率必须设置为115200
- 严格按照通信协议格式进行数据交互
- 修改温度会重新自整定

## 版本历史
- v1.0.0 (2025-08-05): 初始版本，实现基本的TEC温控功能
