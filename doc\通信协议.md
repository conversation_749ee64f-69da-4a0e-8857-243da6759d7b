TEC温控上位机与cpu通信协议

上位机与cpu通信通过uart传输频率信息

通信波特率115200

上位机下发数据到cpu

数据格式：

| byte1 | byte2 | byte3    | byte4~byte15 |
| ----- | ----- | -------- | ------------ |
| 0x5a  | 0xa5  | 控制指令 | 指令数据     |

Byte1~2：固定帧头 0x5a，0xa5

Byte3~15：控制指令及其对应数据

| Byte3（标志信号） | byte4~byte15（控制数据）                                                                                                                      |
| ----------------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| 0x01              | Byte4~byte7   上位机设置的目标温度``byte8~byte15  默认为0x00注：修改温度会重新自整定                                                      |
| 0x02              | Byte4~byte7   上位机设置用户自定义pid——P值 ``Byte8~byte11  上位机设置用户自定义pid——I值``Byte12~byte15 上位机设置用户自定义pid——D值 |
| 0x03              | Byte4         0x01   使用自整定 0x00  使用当前系统内部pid参数                                                                      |
| 0x04              | Byte4~byte7    用户设定输出最大电流（过流保护）                                                                                            |
| 0x05              | Byte4~byte7    用户设定输出最大电压(过压保护)                                                                                              |
| 0x06              | Byte4         0x01    10k热敏电阻 0x02    pt100 0x03    pt1000 0x04    AD590 0x05    LM35（系统初始默认为10k热敏电阻） |
| 0x07              | Byte4~byte7   过温保护最高温度Byte8~byte11   过温保护最低温度                                                                           |
| 0x08              | Byte4~byte7   ntc热敏电阻b值Byte8~byte11  ntc热敏电阻常温阻值（不支持修改）                                                               |
| 0x09              | Byte4         0x01    开启校准 0x00   关闭校准                                                                                   |
| 0xaa              | Byte4         0x01    使能打开 0x00    使能关闭                                                                                 |

CPU返回数据给上位机

数据格式：

| byte1 | byte2 | byte3    | byte4~byte7 |
| ----- | ----- | -------- | ----------- |
| 0x5a  | 0xa5  | 控制指令 | 指令数据    |

Byte1~2：固定帧头 0x5a，0xa5

Byte3~7：控制指令及其对应数据

| Byte3（标志信号） | byte4~byte7（控制数据）                                                                   |
| ----------------- | ----------------------------------------------------------------------------------------- |
| 0x01              | Byte4~byte7    cpu采集当前温度                                                         |
| 0x02              | Byte4~byte7    cpu反馈当前pid参数p值                                                   |
| 0x03              | Byte4~byte7    cpu反馈当前pid参数i值                                                   |
| 0x04              | Byte4~byte7    cpu反馈当前pid参数d值                                                   |
| 0x05              | Byte4      0x01  正常运行 绿色 0x10  异常运行 红色                                 |
| 0x06              | Byte4       0x01  自整定使能打开 0x10  自整定使能关闭                             |
| 0x07              | Byte4~byte7    cpu反馈当前输出电压                                                     |
| 0x08              | Byte4~byte7    cpu反馈当前输出电流                                                     |
| 0x09              | Byte4       0x01    使能打开，开始控温，绿色 0x00    使能关闭，关闭控温，灰色 |
| 0x0a              | Byte4       0x01    校准完毕 0x00   校准进行中                                 |
| 0x0b              | Byte4    0x01    2位小数显示，第3位四舍五入 0x00   3位小数显示                    |
