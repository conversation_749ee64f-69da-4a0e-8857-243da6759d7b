#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TEC温控上位机通信协议解析模块

文件名: protocol.py
作者: mkx
创建日期: 2025-08-05
修改日期: 2025-08-05
版本: v1.0.0

功能描述:
    实现TEC温控设备通信协议的数据包封装和解析功能。
    支持上位机发送指令和CPU返回数据的处理。

协议格式:
    上位机发送: [0x5A][0xA5][控制指令][指令数据(12字节)]
    CPU返回:   [0x5A][0xA5][控制指令][指令数据(4字节)]

主要类:
    ProtocolParser: 协议解析器
    CommandCode: 指令代码枚举
    SensorType: 传感器类型枚举

注意事项:
    - 固定帧头为0x5A 0xA5
    - 数据采用小端序格式
    - 浮点数使用IEEE 754标准
"""

import struct
import logging
from enum import IntEnum
from typing import Optional, Dict, Any, Tuple, Union


class CommandCode(IntEnum):
    """
    指令代码枚举
    
    作者: mkx
    创建日期: 2025-08-05
    
    功能: 定义TEC温控系统的所有指令代码
    """
    # 上位机发送指令
    SET_TARGET_TEMP = 0x01      # 设置目标温度
    SET_PID_PARAMS = 0x02       # 设置PID参数
    SET_AUTO_TUNE = 0x03        # 设置自整定
    SET_MAX_CURRENT = 0x04      # 设置最大电流
    SET_MAX_VOLTAGE = 0x05      # 设置最大电压
    SET_SENSOR_TYPE = 0x06      # 设置传感器类型
    SET_TEMP_PROTECT = 0x07     # 设置过温保护
    SET_NTC_PARAMS = 0x08       # 设置NTC参数
    SET_CALIBRATION = 0x09      # 设置校准
    SET_ENABLE = 0xAA           # 设置使能
    
    # CPU返回指令
    CURRENT_TEMP = 0x01         # 当前温度
    PID_P_VALUE = 0x02          # PID P值
    PID_I_VALUE = 0x03          # PID I值
    PID_D_VALUE = 0x04          # PID D值
    SYSTEM_STATUS = 0x05        # 系统状态
    AUTO_TUNE_STATUS = 0x06     # 自整定状态
    OUTPUT_VOLTAGE = 0x07       # 输出电压
    OUTPUT_CURRENT = 0x08       # 输出电流
    ENABLE_STATUS = 0x09        # 使能状态
    CALIBRATION_STATUS = 0x0A   # 校准状态
    DISPLAY_MODE = 0x0B         # 显示模式


class SensorType(IntEnum):
    """
    传感器类型枚举
    
    作者: mkx
    创建日期: 2025-08-05
    
    功能: 定义支持的温度传感器类型
    """
    NTC_10K = 0x01      # 10K热敏电阻
    PT100 = 0x02        # PT100
    PT1000 = 0x03       # PT1000
    AD590 = 0x04        # AD590
    LM35 = 0x05         # LM35


class ProtocolParser:
    """
    协议解析器类
    
    作者: mkx
    创建日期: 2025-08-05
    
    功能: 提供TEC温控协议的数据包封装和解析功能
    
    属性:
        HEADER_BYTE1: 帧头字节1 (0x5A)
        HEADER_BYTE2: 帧头字节2 (0xA5)
        SEND_PACKET_SIZE: 上位机发送包大小 (15字节)
        RECEIVE_PACKET_SIZE: CPU返回包大小 (7字节)
    
    注意事项:
        - 所有数值采用小端序格式
        - 浮点数使用IEEE 754单精度格式
    """
    
    # 协议常量
    HEADER_BYTE1 = 0x5A
    HEADER_BYTE2 = 0xA5
    SEND_PACKET_SIZE = 15
    RECEIVE_PACKET_SIZE = 7
    
    def __init__(self):
        """
        初始化协议解析器
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 初始化协议解析器相关参数
        """
        self._logger = logging.getLogger(__name__)
    
    def pack_command(self, cmd_code: int, data: bytes = b'') -> bytes:
        """
        封装上位机发送指令
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 将指令代码和数据封装成协议数据包
        
        参数:
            cmd_code (int): 指令代码
            data (bytes): 指令数据，最大12字节
        
        返回值:
            bytes: 封装后的数据包 (15字节)
        
        注意事项:
            - 数据不足12字节时会用0x00填充
            - 数据超过12字节时会被截断
        """
        try:
            # 构建数据包
            packet = bytearray(self.SEND_PACKET_SIZE)
            packet[0] = self.HEADER_BYTE1
            packet[1] = self.HEADER_BYTE2
            packet[2] = cmd_code
            
            # 填充数据部分 (最大12字节)
            data_len = min(len(data), 12)
            packet[3:3+data_len] = data[:data_len]
            
            self._logger.debug(f"封装指令: 0x{cmd_code:02X}, 数据: {data.hex().upper()}")
            return bytes(packet)
            
        except Exception as e:
            self._logger.error(f"封装指令异常: {e}")
            return b''
    
    def parse_response(self, packet: bytes) -> Optional[Tuple[int, bytes]]:
        """
        解析CPU返回数据包
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 解析CPU返回的数据包，提取指令代码和数据
        
        参数:
            packet (bytes): 接收到的数据包
        
        返回值:
            Optional[Tuple[int, bytes]]: 成功返回(指令代码, 数据)，失败返回None
        
        注意事项:
            - 会验证帧头和数据包长度
            - 返回的数据部分为4字节
        """
        try:
            # 检查数据包长度
            if len(packet) < self.RECEIVE_PACKET_SIZE:
                self._logger.warning(f"数据包长度不足: {len(packet)} < {self.RECEIVE_PACKET_SIZE}")
                return None
            
            # 验证帧头
            if packet[0] != self.HEADER_BYTE1 or packet[1] != self.HEADER_BYTE2:
                self._logger.warning(f"帧头错误: {packet[0]:02X} {packet[1]:02X}")
                return None
            
            # 提取指令代码和数据
            cmd_code = packet[2]
            data = packet[3:7]
            
            self._logger.debug(f"解析响应: 0x{cmd_code:02X}, 数据: {data.hex().upper()}")
            return (cmd_code, data)
            
        except Exception as e:
            self._logger.error(f"解析响应异常: {e}")
            return None
    
    def pack_float_data(self, value: float) -> bytes:
        """
        封装浮点数据
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 将浮点数转换为4字节的二进制数据
        
        参数:
            value (float): 要封装的浮点数
        
        返回值:
            bytes: 4字节的二进制数据
        
        注意事项:
            - 使用IEEE 754单精度格式
            - 采用小端序
        """
        try:
            return struct.pack('<f', value)
        except Exception as e:
            self._logger.error(f"封装浮点数异常: {e}")
            return b'\x00\x00\x00\x00'
    
    def unpack_float_data(self, data: bytes) -> float:
        """
        解析浮点数据
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 将4字节二进制数据转换为浮点数
        
        参数:
            data (bytes): 4字节的二进制数据
        
        返回值:
            float: 解析出的浮点数
        
        注意事项:
            - 使用IEEE 754单精度格式
            - 采用小端序
        """
        try:
            if len(data) >= 4:
                return struct.unpack('<f', data[:4])[0]
            else:
                self._logger.warning(f"浮点数据长度不足: {len(data)} < 4")
                return 0.0
        except Exception as e:
            self._logger.error(f"解析浮点数异常: {e}")
            return 0.0
    
    def pack_int_data(self, value: int) -> bytes:
        """
        封装整数数据
        
        作者: mkx
        创建日期: 2025-08-05
        
        功能: 将整数转换为4字节的二进制数据
        
        参数:
            value (int): 要封装的整数
        
        返回值:
            bytes: 4字节的二进制数据
        
        注意事项:
            - 使用32位有符号整数格式
            - 采用小端序
        """
        try:
            return struct.pack('<i', value)
        except Exception as e:
            self._logger.error(f"封装整数异常: {e}")
            return b'\x00\x00\x00\x00'
    
    def unpack_int_data(self, data: bytes) -> int:
        """
        解析整数数据

        作者: mkx
        创建日期: 2025-08-05

        功能: 将4字节二进制数据转换为整数

        参数:
            data (bytes): 4字节的二进制数据

        返回值:
            int: 解析出的整数

        注意事项:
            - 使用32位有符号整数格式
            - 采用小端序
        """
        try:
            if len(data) >= 4:
                return struct.unpack('<i', data[:4])[0]
            else:
                self._logger.warning(f"整数数据长度不足: {len(data)} < 4")
                return 0
        except Exception as e:
            self._logger.error(f"解析整数异常: {e}")
            return 0

    def create_set_target_temp_command(self, temperature: float) -> bytes:
        """
        创建设置目标温度指令

        作者: mkx
        创建日期: 2025-08-05

        功能: 创建设置目标温度的指令数据包

        参数:
            temperature (float): 目标温度值

        返回值:
            bytes: 指令数据包

        注意事项:
            - 修改温度会重新自整定
        """
        temp_data = self.pack_float_data(temperature)
        padding = b'\x00' * 8  # byte8~byte15默认为0x00
        data = temp_data + padding
        return self.pack_command(CommandCode.SET_TARGET_TEMP, data)

    def create_set_pid_params_command(self, p_value: float, i_value: float, d_value: float) -> bytes:
        """
        创建设置PID参数指令

        作者: mkx
        创建日期: 2025-08-05

        功能: 创建设置用户自定义PID参数的指令数据包

        参数:
            p_value (float): P值
            i_value (float): I值
            d_value (float): D值

        返回值:
            bytes: 指令数据包
        """
        p_data = self.pack_float_data(p_value)
        i_data = self.pack_float_data(i_value)
        d_data = self.pack_float_data(d_value)
        data = p_data + i_data + d_data
        return self.pack_command(CommandCode.SET_PID_PARAMS, data)

    def create_set_auto_tune_command(self, enable: bool) -> bytes:
        """
        创建设置自整定指令

        作者: mkx
        创建日期: 2025-08-05

        功能: 创建设置自整定使能的指令数据包

        参数:
            enable (bool): True为使用自整定，False为使用当前系统内部PID参数

        返回值:
            bytes: 指令数据包
        """
        enable_byte = b'\x01' if enable else b'\x00'
        padding = b'\x00' * 11
        data = enable_byte + padding
        return self.pack_command(CommandCode.SET_AUTO_TUNE, data)

    def create_set_max_current_command(self, max_current: float) -> bytes:
        """
        创建设置最大电流指令

        作者: mkx
        创建日期: 2025-08-05

        功能: 创建设置输出最大电流(过流保护)的指令数据包

        参数:
            max_current (float): 最大电流值

        返回值:
            bytes: 指令数据包
        """
        current_data = self.pack_float_data(max_current)
        padding = b'\x00' * 8
        data = current_data + padding
        return self.pack_command(CommandCode.SET_MAX_CURRENT, data)

    def create_set_max_voltage_command(self, max_voltage: float) -> bytes:
        """
        创建设置最大电压指令

        作者: mkx
        创建日期: 2025-08-05

        功能: 创建设置输出最大电压(过压保护)的指令数据包

        参数:
            max_voltage (float): 最大电压值

        返回值:
            bytes: 指令数据包
        """
        voltage_data = self.pack_float_data(max_voltage)
        padding = b'\x00' * 8
        data = voltage_data + padding
        return self.pack_command(CommandCode.SET_MAX_VOLTAGE, data)

    def create_set_sensor_type_command(self, sensor_type: SensorType) -> bytes:
        """
        创建设置传感器类型指令

        作者: mkx
        创建日期: 2025-08-05

        功能: 创建设置温度传感器类型的指令数据包

        参数:
            sensor_type (SensorType): 传感器类型

        返回值:
            bytes: 指令数据包
        """
        sensor_byte = bytes([sensor_type])
        padding = b'\x00' * 11
        data = sensor_byte + padding
        return self.pack_command(CommandCode.SET_SENSOR_TYPE, data)

    def create_set_temp_protect_command(self, max_temp: float, min_temp: float) -> bytes:
        """
        创建设置过温保护指令

        作者: mkx
        创建日期: 2025-08-05

        功能: 创建设置过温保护最高和最低温度的指令数据包

        参数:
            max_temp (float): 过温保护最高温度
            min_temp (float): 过温保护最低温度

        返回值:
            bytes: 指令数据包
        """
        max_temp_data = self.pack_float_data(max_temp)
        min_temp_data = self.pack_float_data(min_temp)
        padding = b'\x00' * 4
        data = max_temp_data + min_temp_data + padding
        return self.pack_command(CommandCode.SET_TEMP_PROTECT, data)

    def create_set_ntc_params_command(self, b_value: float, resistance_25c: float) -> bytes:
        """
        创建设置NTC参数指令

        作者: mkx
        创建日期: 2025-08-05

        功能: 创建设置NTC热敏电阻参数的指令数据包

        参数:
            b_value (float): NTC热敏电阻B值
            resistance_25c (float): NTC热敏电阻常温阻值(不支持修改)

        返回值:
            bytes: 指令数据包
        """
        b_value_data = self.pack_float_data(b_value)
        resistance_data = self.pack_float_data(resistance_25c)
        padding = b'\x00' * 4
        data = b_value_data + resistance_data + padding
        return self.pack_command(CommandCode.SET_NTC_PARAMS, data)

    def create_set_calibration_command(self, enable: bool) -> bytes:
        """
        创建设置校准指令

        作者: mkx
        创建日期: 2025-08-05

        功能: 创建设置校准使能的指令数据包

        参数:
            enable (bool): True为开启校准，False为关闭校准

        返回值:
            bytes: 指令数据包
        """
        enable_byte = b'\x01' if enable else b'\x00'
        padding = b'\x00' * 11
        data = enable_byte + padding
        return self.pack_command(CommandCode.SET_CALIBRATION, data)

    def create_set_enable_command(self, enable: bool) -> bytes:
        """
        创建设置使能指令

        作者: mkx
        创建日期: 2025-08-05

        功能: 创建设置系统使能的指令数据包

        参数:
            enable (bool): True为使能打开，False为使能关闭

        返回值:
            bytes: 指令数据包
        """
        enable_byte = b'\x01' if enable else b'\x00'
        padding = b'\x00' * 11
        data = enable_byte + padding
        return self.pack_command(CommandCode.SET_ENABLE, data)

    def parse_temperature_response(self, data: bytes) -> float:
        """
        解析温度响应数据

        作者: mkx
        创建日期: 2025-08-05

        功能: 解析CPU返回的温度数据

        参数:
            data (bytes): 4字节温度数据

        返回值:
            float: 温度值
        """
        return self.unpack_float_data(data)

    def parse_pid_response(self, data: bytes) -> float:
        """
        解析PID参数响应数据

        作者: mkx
        创建日期: 2025-08-05

        功能: 解析CPU返回的PID参数数据

        参数:
            data (bytes): 4字节PID参数数据

        返回值:
            float: PID参数值
        """
        return self.unpack_float_data(data)

    def parse_status_response(self, data: bytes) -> Dict[str, Any]:
        """
        解析状态响应数据

        作者: mkx
        创建日期: 2025-08-05

        功能: 解析CPU返回的各种状态数据

        参数:
            data (bytes): 状态数据

        返回值:
            Dict[str, Any]: 状态信息字典
        """
        if len(data) < 1:
            return {}

        status_byte = data[0]

        # 根据不同的状态类型解析
        if status_byte == 0x01:
            return {"status": "normal", "color": "green", "description": "正常运行"}
        elif status_byte == 0x10:
            return {"status": "error", "color": "red", "description": "异常运行"}
        elif status_byte == 0x00:
            return {"status": "disabled", "color": "gray", "description": "使能关闭"}
        else:
            return {"status": "unknown", "color": "gray", "description": f"未知状态: 0x{status_byte:02X}"}
